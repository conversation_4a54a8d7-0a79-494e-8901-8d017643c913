{"name": "@vitejs/plugin-vue", "version": "1.6.0", "license": "MIT", "author": "<PERSON>", "files": ["dist"], "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "tsc -p . -w --incremental", "build": "rimraf dist && run-s build-bundle build-types", "build-bundle": "esbuild src/index.ts --bundle --platform=node --target=node12 --external:@vue/compiler-sfc --outfile=dist/index.js", "build-types": "tsc -p . --emitDeclarationOnly --outDir temp && api-extractor run && rimraf temp", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package plugin-vue", "release": "node ../../scripts/release.js"}, "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/plugin-vue"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/packages/plugin-vue#readme", "peerDependencies": {"@vue/compiler-sfc": "^3.2.6"}, "devDependencies": {"@rollup/pluginutils": "^4.1.1", "@types/hash-sum": "^1.0.0", "@vue/compiler-sfc": "^3.2.6", "debug": "^4.3.2", "hash-sum": "^2.0.0", "rollup": "^2.38.5", "slash": "^3.0.0", "source-map": "^0.6.1"}}